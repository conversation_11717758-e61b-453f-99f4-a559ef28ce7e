"use client"

import { Download, Printer } from "lucide-react"
import { useState } from "react"

export default function DownloadButton() {
  const [isProcessing, setIsProcessing] = useState(false)

  const handleDownload = () => {
    setIsProcessing(true)

    try {
      // Use browser's print functionality to save as PDF
      // This opens the print dialog where users can choose "Save as PDF"
      window.print()
    } catch (error) {
      console.error("Error opening print dialog:", error)
      alert("Failed to open print dialog. Please try using Ctrl+P (Cmd+P on Mac) to print/save as PDF.")
    } finally {
      setIsProcessing(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  return (
    <div className="flex gap-2">
      <button
        onClick={handleDownload}
        disabled={isProcessing}
        className="flex items-center gap-2 bg-gradient-to-r from-cyan-600 to-cyan-500 hover:from-cyan-700 hover:to-cyan-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 hover:shadow-lg disabled:opacity-70"
      >
        {isProcessing ? (
          <>
            <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            <span>Processing...</span>
          </>
        ) : (
          <>
            <Download className="h-4 w-4" />
            <span>Save as PDF</span>
          </>
        )}
      </button>

      <button
        onClick={handlePrint}
        className="flex items-center gap-2 bg-gradient-to-r from-gray-600 to-gray-500 hover:from-gray-700 hover:to-gray-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 hover:shadow-lg"
      >
        <Printer className="h-4 w-4" />
        <span>Print</span>
      </button>
    </div>
  )
}
