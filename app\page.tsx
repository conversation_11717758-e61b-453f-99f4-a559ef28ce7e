import { Mail, Phone, Calendar, Briefcase, GraduationCap, Code, Languages, Star, Layers } from "lucide-react"
import DownloadButton from "@/components/download-button"

export default function CV() {
  return (
    <div className="bg-gradient-to-br from-cyan-50 to-white min-h-screen p-6 md:p-10 max-w-5xl mx-auto relative">
      {/* Download Button */}
      <div className="absolute top-6 right-6 md:top-10 md:right-10 z-10">
        <DownloadButton />
      </div>

      {/* CV Container */}
      <div id="cv-container">
        {/* Header */}
        <header className="mb-8 flex flex-col md:flex-row justify-between items-center">
          <div>
            <h1 className="text-4xl md:text-5xl font-bold text-cyan-800">TRAN TIEN ANH</h1>
            <h2 className="text-xl md:text-2xl font-medium text-cyan-600 mt-2">Quality Assurance Engineer</h2>

            <div className="mt-4 flex flex-wrap gap-4">
              <div className="flex items-center text-gray-700">
                <Phone className="h-4 w-4 mr-2 text-cyan-600" />
                <span>+84 333900298</span>
              </div>
              <div className="flex items-center text-gray-700">
                <Mail className="h-4 w-4 mr-2 text-cyan-600" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center text-gray-700">
                <Calendar className="h-4 w-4 mr-2 text-cyan-600" />
                <span>April 30, 1998</span>
              </div>
            </div>
          </div>
          <div className="mt-6 md:mt-0">
            <div className="w-32 h-32 md:w-40 md:h-40 rounded-full overflow-hidden border-4 border-cyan-300 shadow-lg shadow-cyan-100">
              <img
                src="/placeholder.svg?height=160&width=160"
                alt="Tran Tien Anh"
                className="w-full h-full object-cover"
              />
            </div>
          </div>
        </header>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Left Sidebar */}
          <div className="md:col-span-1 space-y-8">
            {/* Technical Skills */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-cyan-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <Code className="h-5 w-5 mr-2 text-cyan-500" />
                Technical Skills
              </h3>
              <div className="space-y-3 text-gray-700">
                <div>
                  <span className="font-medium">Database Management:</span> SQL Server, PostgreSQL
                </div>
                <div>
                  <span className="font-medium">API Testing:</span> Postman
                </div>
                <div>
                  <span className="font-medium">Automation Tools:</span> Selenium, Cypress, Playwright
                </div>
                <div>
                  <span className="font-medium">Testing Frameworks:</span> TestNG, NUnit, Cucumber
                </div>
                <div>
                  <span className="font-medium">Development Environments:</span> IntelliJ IDEA, Visual Studio Code
                </div>
                <div>
                  <span className="font-medium">Programming Languages:</span> Java, JavaScript, TypeScript, C#
                </div>
                <div>
                  <span className="font-medium">Version Control Systems:</span> Git, GitLab
                </div>
                <div>
                  <span className="font-medium">Project Management Tools:</span> Jira, Azure DevOps
                </div>
                <div>
                  <span className="font-medium">Additional Tools:</span> Redis, Kafka
                </div>
              </div>
            </section>

            {/* Languages */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-cyan-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <Languages className="h-5 w-5 mr-2 text-cyan-500" />
                Languages
              </h3>
              <div className="space-y-3 text-gray-700">
                <div>
                  <span className="font-medium">English:</span> TOEIC 700 (May 2020)
                </div>
                <div>
                  <span className="font-medium">Chinese:</span> HSK 3
                </div>
              </div>
            </section>

            {/* Education */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-cyan-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <GraduationCap className="h-5 w-5 mr-2 text-cyan-500" />
                Education & Certifications
              </h3>
              <div className="space-y-4 text-gray-700">
                <div>
                  <div className="font-medium">Bachelor of Engineering</div>
                  <div className="text-sm">
                    University of Engineering and Technology – Vietnam National University, Hanoi
                  </div>
                  <div className="text-sm">Faculty of Engineering Physics and Nanotechnology</div>
                  <div className="text-sm text-cyan-600">September 2016 - June 2020</div>
                </div>
                <div>
                  <div className="font-medium">Certificate in Manual Testing</div>
                  <div className="text-sm">TesterTop Center</div>
                  <div className="text-sm text-cyan-600">October 2020 - January 2021</div>
                </div>
              </div>
            </section>

            {/* Core Competencies */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-cyan-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <Layers className="h-5 w-5 mr-2 text-cyan-500" />
                Core Competencies
              </h3>
              <div className="space-y-3">
                <div className="bg-cyan-50 p-3 rounded-md">
                  <div className="font-medium text-cyan-800">Manual Testing</div>
                  <p className="text-sm text-gray-700">Comprehensive test case design and execution</p>
                </div>
                <div className="bg-cyan-50 p-3 rounded-md">
                  <div className="font-medium text-cyan-800">API Testing</div>
                  <p className="text-sm text-gray-700">RESTful API validation and integration testing</p>
                </div>
                <div className="bg-cyan-50 p-3 rounded-md">
                  <div className="font-medium text-cyan-800">Database Testing</div>
                  <p className="text-sm text-gray-700">SQL query validation and data integrity</p>
                </div>
                <div className="bg-cyan-50 p-3 rounded-md">
                  <div className="font-medium text-cyan-800">Test Automation</div>
                  <p className="text-sm text-gray-700">Automated test script development</p>
                </div>
              </div>
            </section>
          </div>

          {/* Main Content */}
          <div className="md:col-span-2 space-y-8">
            {/* Work Experience */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-orange-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <Briefcase className="h-5 w-5 mr-2 text-orange-500" />
                Work Experience
              </h3>
              <div className="space-y-6">
                {/* Current Job */}
                <div className="border-l-2 border-cyan-300 pl-4 py-1">
                  <div className="font-medium text-cyan-800">Quality Assurance Engineer</div>
                  <div className="text-cyan-600">Merchize JSC</div>
                  <div className="text-sm text-gray-500">June 2024 - Present</div>

                  <div className="mt-3">
                    <div className="font-medium text-gray-700">Project: Dex3.ai (DEX Platform)</div>
                    <ul className="list-disc list-inside mt-2 text-gray-700 ml-2 space-y-1">
                      <li>
                        Designed and executed test cases for major features including trading, copytrade, deep signal,
                        and pump to moon.
                      </li>
                      <li>Simulated complex trading scenarios to validate system stability.</li>
                      <li>Set up API testing flows in Postman to ensure system stability post-integration.</li>
                      <li>Built automated UI tests to reduce regression testing time.</li>
                      <li>Wrote SQL queries to validate data correctness from databases.</li>
                    </ul>
                    <div className="mt-2">
                      <span className="font-medium text-cyan-700">Tools Used:</span>
                      <span className="text-gray-700"> Postman, PostgreSQL, Playwright, Redis, Kafka, Jira</span>
                    </div>
                  </div>
                </div>

                {/* Previous Job */}
                <div className="border-l-2 border-cyan-300 pl-4 py-1">
                  <div className="font-medium text-cyan-800">Quality Assurance Engineer</div>
                  <div className="text-cyan-600">IMIP Technology And Solution Consultancy JSC</div>
                  <div className="text-sm text-gray-500">January 2021 - May 2024</div>

                  <div className="mt-3 space-y-4">
                    {/* Restaurant365 Project */}
                    <div>
                      <div className="font-medium text-cyan-700">Restaurant365 (ERP System)</div>
                      <ul className="list-disc list-inside mt-1 text-gray-700 ml-2 space-y-1">
                        <li>
                          Designed and executed manual test cases covering accounting, inventory management, purchasing,
                          and reporting functionalities.
                        </li>
                        <li>
                          Maintained existing automation scripts and developed additional scripts for new
                          functionalities.
                        </li>
                        <li>
                          Performed thorough database validations and API integration tests to ensure data integrity and
                          system reliability.
                        </li>
                      </ul>
                      <div className="mt-1">
                        <span className="text-sm font-medium text-cyan-700">Tools Used:</span>
                        <span className="text-sm text-gray-700"> Postman, PostgreSQL, Cypress, Jira</span>
                      </div>
                    </div>

                    {/* BQE CORE Project */}
                    <div>
                      <div className="font-medium text-cyan-700">BQE CORE (Financial Management)</div>
                      <ul className="list-disc list-inside mt-1 text-gray-700 ml-2 space-y-1">
                        <li>
                          Conducted detailed functional testing and regression tests on billing, invoicing, and expense
                          management modules.
                        </li>
                        <li>
                          Maintained existing automation scripts and developed additional ones for newly implemented
                          functionalities.
                        </li>
                        <li>
                          Collaborated closely with developers to track, reproduce, and resolve software issues
                          efficiently.
                        </li>
                      </ul>
                      <div className="mt-1">
                        <span className="text-sm font-medium text-cyan-700">Tools Used:</span>
                        <span className="text-sm text-gray-700"> Postman, SQL Server, Selenium, Jira</span>
                      </div>
                    </div>

                    {/* Threat Response Platform Project */}
                    <div>
                      <div className="font-medium text-cyan-700">Threat Response Platform (Cybersecurity - SIEM)</div>
                      <ul className="list-disc list-inside mt-1 text-gray-700 ml-2 space-y-1">
                        <li>
                          Executed comprehensive test scenarios for monitoring security events, analyzing threats, and
                          validating system integrations.
                        </li>
                        <li>
                          Documented security vulnerabilities clearly and facilitated timely remediation by development
                          teams.
                        </li>
                      </ul>
                      <div className="mt-1">
                        <span className="text-sm font-medium text-cyan-700">Tools Used:</span>
                        <span className="text-sm text-gray-700"> Postman, PostgreSQL, Jira</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>

            {/* Project Highlights */}
            <section className="bg-white p-6 rounded-lg shadow-md border-l-4 border-orange-400 hover:shadow-lg transition-shadow duration-300">
              <h3 className="text-lg font-semibold text-cyan-700 mb-4 flex items-center">
                <Star className="h-5 w-5 mr-2 text-orange-500" />
                Key Achievements
              </h3>
              <div className="space-y-4">
                <div className="border-b border-cyan-100 pb-4">
                  <div className="font-medium text-cyan-800">Dex3.ai Platform Testing</div>
                  <ul className="list-disc list-inside mt-1 text-gray-700 ml-2">
                    <li>
                      Successfully tested complex DeFi features including trading algorithms and signal processing
                    </li>
                    <li>Implemented comprehensive API testing framework reducing integration issues by 40%</li>
                    <li>Developed automated UI test suite covering critical user journeys</li>
                  </ul>
                </div>

                <div className="border-b border-cyan-100 pb-4">
                  <div className="font-medium text-cyan-800">ERP System Validation</div>
                  <ul className="list-disc list-inside mt-1 text-gray-700 ml-2">
                    <li>Ensured data integrity across multiple restaurant management modules</li>
                    <li>Maintained and enhanced automation test coverage for continuous integration</li>
                  </ul>
                </div>

                <div className="border-b border-cyan-100 pb-4">
                  <div className="font-medium text-cyan-800">Financial System Testing</div>
                  <ul className="list-disc list-inside mt-1 text-gray-700 ml-2">
                    <li>Validated complex billing and invoicing workflows with zero critical defects in production</li>
                    <li>Collaborated effectively with development teams to resolve issues promptly</li>
                  </ul>
                </div>

                <div>
                  <div className="font-medium text-cyan-800">Security Testing Excellence</div>
                  <ul className="list-disc list-inside mt-1 text-gray-700 ml-2">
                    <li>Identified and documented critical security vulnerabilities in SIEM systems</li>
                    <li>Contributed to improved threat detection and response capabilities</li>
                  </ul>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </div>
  )
}
