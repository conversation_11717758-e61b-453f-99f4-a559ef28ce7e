"use client"

import { Printer } from "lucide-react"

export default function PrintButton() {
  const handlePrint = () => {
    window.print()
  }

  return (
    <button
      onClick={handlePrint}
      className="flex items-center gap-2 bg-gradient-to-r from-gray-600 to-gray-500 hover:from-gray-700 hover:to-gray-600 text-white px-4 py-2 rounded-md shadow-md transition-all duration-300 hover:shadow-lg ml-2 no-print"
    >
      <Printer className="h-4 w-4" />
      <span>Print CV</span>
    </button>
  )
}
